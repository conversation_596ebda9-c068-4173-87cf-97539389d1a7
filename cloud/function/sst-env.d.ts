/* This file is auto-generated by SST. Do not edit. */
/* tslint:disable */
/* eslint-disable */
/* deno-fmt-ignore-file */

import "sst"
declare module "sst" {
  export interface Resource {
    "ANTHROPIC_API_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "AUTH_API_URL": {
      "type": "sst.sst.Linkable"
      "value": string
    }
    "Console": {
      "type": "sst.cloudflare.SolidStart"
      "url": string
    }
    "DATABASE_PASSWORD": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "DATABASE_USERNAME": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "Database": {
      "database": string
      "host": string
      "password": string
      "port": number
      "type": "sst.sst.Linkable"
      "username": string
    }
    "GITHUB_APP_ID": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "GITHUB_APP_PRIVATE_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "GITHUB_CLIENT_ID_CONSOLE": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "GITHUB_CLIENT_SECRET_CONSOLE": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "GOOGLE_CLIENT_ID": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "OPENAI_API_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "STRIPE_SECRET_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "STRIPE_WEBHOOK_SECRET": {
      "type": "sst.sst.Linkable"
      "value": string
    }
    "Web": {
      "type": "sst.cloudflare.Astro"
      "url": string
    }
    "ZHIPU_API_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
  }
}
// cloudflare 
import * as cloudflare from "@cloudflare/workers-types";
declare module "sst" {
  export interface Resource {
    "Api": cloudflare.Service
    "AuthApi": cloudflare.Service
    "AuthStorage": cloudflare.KVNamespace
    "Bucket": cloudflare.R2Bucket
    "GatewayApi": cloudflare.Service
  }
}

import "sst"
export {}