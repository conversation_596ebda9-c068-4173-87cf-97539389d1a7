{"$schema": "https://json.schemastore.org/package.json", "name": "@opencode/cloud-core", "version": "0.5.29", "private": true, "type": "module", "dependencies": {"@aws-sdk/client-sts": "3.782.0", "@opencode/cloud-resource": "workspace:*", "drizzle-orm": "0.41.0", "postgres": "3.4.7", "stripe": "18.0.0", "ulid": "3.0.0"}, "exports": {"./*": "./src/*"}, "scripts": {"db": "sst shell drizzle-kit"}, "devDependencies": {"drizzle-kit": "0.30.5"}}