## IMPORTANT

- Try to keep things in one function unless composable or reusable
- DO NOT do unnecessary destructuring of variables
- DO NOT use `else` statements unless necessary
- DO NOT use `try`/`catch` if it can be avoided
- AVOID `try`/`catch` where possible
- AVOID `else` statements
- <PERSON><PERSON><PERSON> using `any` type
- AVOID `let` statements
- PREFER single word variable names where possible
- Use as many bun apis as possible like Bun.file()


## Testing
## /test Command Specification

### Overview
The `/test` command is responsible for **creating, running, and reporting Playwright MCP tests**.  

⚠️ **Mandatory Requirement:**  
Every run must produce:
- A **Playwright HTML report**  
- **Video recordings** of the test run  

Both outputs must be saved and included in the final summary.  
If either is missing, the run is considered **incomplete**.
### Behavior

#### 1. Generate Test File
- Create a Playwright test file in **TypeScript**.  
- Save it in: `./playwright_run_experiment/`
#### 2. Run the Tests
- Execute with **Playwright MCP** using **only**: `--config ./mcp_config/playwright.config.ts`
- **Do not** pass any other CLI flags (e.g., `--reporter`, `--trace`, `--video`, `--output`, `--config=…` elsewhere).
- **Do not** set env vars that override reporting (e.g., `PLAYWRIGHT_HTML_REPORT`) or artifacts.


#### 3. Save Output (Report + Video Required)
- Save the **Playwright HTML report**  and the **video recordings** base on the configuration file.
- Do not skip — both outputs are required every time.

#### 4. Return Results
- After the run, print a short **summary** that includes:
- Path to the **HTML report**  
- Path(s) to the **video recordings**




