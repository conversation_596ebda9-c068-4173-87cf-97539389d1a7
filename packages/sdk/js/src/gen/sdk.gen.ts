// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from "./client/index.js"
import type {
  EventSubscribeData,
  EventSubscribeResponses,
  AppGetData,
  AppGetResponses,
  AppInitData,
  AppInitResponses,
  ConfigGetData,
  ConfigGetResponses,
  SessionListData,
  SessionListResponses,
  SessionCreateData,
  SessionCreateResponses,
  SessionCreateErrors,
  SessionDeleteData,
  SessionDeleteResponses,
  SessionGetData,
  SessionGetResponses,
  SessionUpdateData,
  SessionUpdateResponses,
  SessionChildrenData,
  SessionChildrenResponses,
  SessionInitData,
  SessionInitResponses,
  SessionAbortData,
  SessionAbortResponses,
  SessionUnshareData,
  SessionUnshareResponses,
  SessionShareData,
  SessionShareResponses,
  SessionSummarizeData,
  SessionSummarizeResponses,
  SessionMessagesData,
  SessionMessagesResponses,
  SessionChatData,
  SessionChatResponses,
  SessionMessageData,
  SessionMessageResponses,
  SessionCommandData,
  SessionCommandResponses,
  SessionShellData,
  SessionShellResponses,
  SessionRevertData,
  SessionRevertResponses,
  SessionUnrevertData,
  SessionUnrevertResponses,
  PostSessionByIdPermissionsByPermissionIdData,
  PostSessionByIdPermissionsByPermissionIdResponses,
  CommandListData,
  CommandListResponses,
  ConfigProvidersData,
  ConfigProvidersResponses,
  FindTextData,
  FindTextResponses,
  FindFilesData,
  FindFilesResponses,
  FindSymbolsData,
  FindSymbolsResponses,
  FileListData,
  FileListResponses,
  FileReadData,
  FileReadResponses,
  FileStatusData,
  FileStatusResponses,
  AppLogData,
  AppLogResponses,
  AppAgentsData,
  AppAgentsResponses,
  TuiAppendPromptData,
  TuiAppendPromptResponses,
  TuiOpenHelpData,
  TuiOpenHelpResponses,
  TuiOpenSessionsData,
  TuiOpenSessionsResponses,
  TuiOpenThemesData,
  TuiOpenThemesResponses,
  TuiOpenModelsData,
  TuiOpenModelsResponses,
  TuiSubmitPromptData,
  TuiSubmitPromptResponses,
  TuiClearPromptData,
  TuiClearPromptResponses,
  TuiExecuteCommandData,
  TuiExecuteCommandResponses,
  TuiShowToastData,
  TuiShowToastResponses,
  AuthSetData,
  AuthSetResponses,
  AuthSetErrors,
} from "./types.gen.js"
import { client as _heyApiClient } from "./client.gen.js"

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<
  TData,
  ThrowOnError
> & {
  /**
   * You can provide a client instance returned by `createClient()` instead of
   * individual options. This might be also useful if you want to implement a
   * custom client.
   */
  client?: Client
  /**
   * You can pass arbitrary values through the `meta` object. This can be
   * used to access values that aren't defined as part of the SDK function.
   */
  meta?: Record<string, unknown>
}

class _HeyApiClient {
  protected _client: Client = _heyApiClient

  constructor(args?: { client?: Client }) {
    if (args?.client) {
      this._client = args.client
    }
  }
}

class Event extends _HeyApiClient {
  /**
   * Get events
   */
  public subscribe<ThrowOnError extends boolean = false>(options?: Options<EventSubscribeData, ThrowOnError>) {
    return (options?.client ?? this._client).get.sse<EventSubscribeResponses, unknown, ThrowOnError>({
      url: "/event",
      ...options,
    })
  }
}

class App extends _HeyApiClient {
  /**
   * Get app info
   */
  public get<ThrowOnError extends boolean = false>(options?: Options<AppGetData, ThrowOnError>) {
    return (options?.client ?? this._client).get<AppGetResponses, unknown, ThrowOnError>({
      url: "/app",
      ...options,
    })
  }

  /**
   * Initialize the app
   */
  public init<ThrowOnError extends boolean = false>(options?: Options<AppInitData, ThrowOnError>) {
    return (options?.client ?? this._client).post<AppInitResponses, unknown, ThrowOnError>({
      url: "/app/init",
      ...options,
    })
  }

  /**
   * Write a log entry to the server logs
   */
  public log<ThrowOnError extends boolean = false>(options?: Options<AppLogData, ThrowOnError>) {
    return (options?.client ?? this._client).post<AppLogResponses, unknown, ThrowOnError>({
      url: "/log",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
    })
  }

  /**
   * List all agents
   */
  public agents<ThrowOnError extends boolean = false>(options?: Options<AppAgentsData, ThrowOnError>) {
    return (options?.client ?? this._client).get<AppAgentsResponses, unknown, ThrowOnError>({
      url: "/agent",
      ...options,
    })
  }
}

class Config extends _HeyApiClient {
  /**
   * Get config info
   */
  public get<ThrowOnError extends boolean = false>(options?: Options<ConfigGetData, ThrowOnError>) {
    return (options?.client ?? this._client).get<ConfigGetResponses, unknown, ThrowOnError>({
      url: "/config",
      ...options,
    })
  }

  /**
   * List all providers
   */
  public providers<ThrowOnError extends boolean = false>(options?: Options<ConfigProvidersData, ThrowOnError>) {
    return (options?.client ?? this._client).get<ConfigProvidersResponses, unknown, ThrowOnError>({
      url: "/config/providers",
      ...options,
    })
  }
}

class Session extends _HeyApiClient {
  /**
   * List all sessions
   */
  public list<ThrowOnError extends boolean = false>(options?: Options<SessionListData, ThrowOnError>) {
    return (options?.client ?? this._client).get<SessionListResponses, unknown, ThrowOnError>({
      url: "/session",
      ...options,
    })
  }

  /**
   * Create a new session
   */
  public create<ThrowOnError extends boolean = false>(options?: Options<SessionCreateData, ThrowOnError>) {
    return (options?.client ?? this._client).post<SessionCreateResponses, SessionCreateErrors, ThrowOnError>({
      url: "/session",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
    })
  }

  /**
   * Delete a session and all its data
   */
  public delete<ThrowOnError extends boolean = false>(options: Options<SessionDeleteData, ThrowOnError>) {
    return (options.client ?? this._client).delete<SessionDeleteResponses, unknown, ThrowOnError>({
      url: "/session/{id}",
      ...options,
    })
  }

  /**
   * Get session
   */
  public get<ThrowOnError extends boolean = false>(options: Options<SessionGetData, ThrowOnError>) {
    return (options.client ?? this._client).get<SessionGetResponses, unknown, ThrowOnError>({
      url: "/session/{id}",
      ...options,
    })
  }

  /**
   * Update session properties
   */
  public update<ThrowOnError extends boolean = false>(options: Options<SessionUpdateData, ThrowOnError>) {
    return (options.client ?? this._client).patch<SessionUpdateResponses, unknown, ThrowOnError>({
      url: "/session/{id}",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }

  /**
   * Get a session's children
   */
  public children<ThrowOnError extends boolean = false>(options: Options<SessionChildrenData, ThrowOnError>) {
    return (options.client ?? this._client).get<SessionChildrenResponses, unknown, ThrowOnError>({
      url: "/session/{id}/children",
      ...options,
    })
  }

  /**
   * Analyze the app and create an AGENTS.md file
   */
  public init<ThrowOnError extends boolean = false>(options: Options<SessionInitData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionInitResponses, unknown, ThrowOnError>({
      url: "/session/{id}/init",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }

  /**
   * Abort a session
   */
  public abort<ThrowOnError extends boolean = false>(options: Options<SessionAbortData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionAbortResponses, unknown, ThrowOnError>({
      url: "/session/{id}/abort",
      ...options,
    })
  }

  /**
   * Unshare the session
   */
  public unshare<ThrowOnError extends boolean = false>(options: Options<SessionUnshareData, ThrowOnError>) {
    return (options.client ?? this._client).delete<SessionUnshareResponses, unknown, ThrowOnError>({
      url: "/session/{id}/share",
      ...options,
    })
  }

  /**
   * Share a session
   */
  public share<ThrowOnError extends boolean = false>(options: Options<SessionShareData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionShareResponses, unknown, ThrowOnError>({
      url: "/session/{id}/share",
      ...options,
    })
  }

  /**
   * Summarize the session
   */
  public summarize<ThrowOnError extends boolean = false>(options: Options<SessionSummarizeData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionSummarizeResponses, unknown, ThrowOnError>({
      url: "/session/{id}/summarize",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }

  /**
   * List messages for a session
   */
  public messages<ThrowOnError extends boolean = false>(options: Options<SessionMessagesData, ThrowOnError>) {
    return (options.client ?? this._client).get<SessionMessagesResponses, unknown, ThrowOnError>({
      url: "/session/{id}/message",
      ...options,
    })
  }

  /**
   * Create and send a new message to a session
   */
  public chat<ThrowOnError extends boolean = false>(options: Options<SessionChatData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionChatResponses, unknown, ThrowOnError>({
      url: "/session/{id}/message",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }

  /**
   * Get a message from a session
   */
  public message<ThrowOnError extends boolean = false>(options: Options<SessionMessageData, ThrowOnError>) {
    return (options.client ?? this._client).get<SessionMessageResponses, unknown, ThrowOnError>({
      url: "/session/{id}/message/{messageID}",
      ...options,
    })
  }

  /**
   * Send a new command to a session
   */
  public command<ThrowOnError extends boolean = false>(options: Options<SessionCommandData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionCommandResponses, unknown, ThrowOnError>({
      url: "/session/{id}/command",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }

  /**
   * Run a shell command
   */
  public shell<ThrowOnError extends boolean = false>(options: Options<SessionShellData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionShellResponses, unknown, ThrowOnError>({
      url: "/session/{id}/shell",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }

  /**
   * Revert a message
   */
  public revert<ThrowOnError extends boolean = false>(options: Options<SessionRevertData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionRevertResponses, unknown, ThrowOnError>({
      url: "/session/{id}/revert",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }

  /**
   * Restore all reverted messages
   */
  public unrevert<ThrowOnError extends boolean = false>(options: Options<SessionUnrevertData, ThrowOnError>) {
    return (options.client ?? this._client).post<SessionUnrevertResponses, unknown, ThrowOnError>({
      url: "/session/{id}/unrevert",
      ...options,
    })
  }
}

class Command extends _HeyApiClient {
  /**
   * List all commands
   */
  public list<ThrowOnError extends boolean = false>(options?: Options<CommandListData, ThrowOnError>) {
    return (options?.client ?? this._client).get<CommandListResponses, unknown, ThrowOnError>({
      url: "/command",
      ...options,
    })
  }
}

class Find extends _HeyApiClient {
  /**
   * Find text in files
   */
  public text<ThrowOnError extends boolean = false>(options: Options<FindTextData, ThrowOnError>) {
    return (options.client ?? this._client).get<FindTextResponses, unknown, ThrowOnError>({
      url: "/find",
      ...options,
    })
  }

  /**
   * Find files
   */
  public files<ThrowOnError extends boolean = false>(options: Options<FindFilesData, ThrowOnError>) {
    return (options.client ?? this._client).get<FindFilesResponses, unknown, ThrowOnError>({
      url: "/find/file",
      ...options,
    })
  }

  /**
   * Find workspace symbols
   */
  public symbols<ThrowOnError extends boolean = false>(options: Options<FindSymbolsData, ThrowOnError>) {
    return (options.client ?? this._client).get<FindSymbolsResponses, unknown, ThrowOnError>({
      url: "/find/symbol",
      ...options,
    })
  }
}

class File extends _HeyApiClient {
  /**
   * List files and directories
   */
  public list<ThrowOnError extends boolean = false>(options: Options<FileListData, ThrowOnError>) {
    return (options.client ?? this._client).get<FileListResponses, unknown, ThrowOnError>({
      url: "/file",
      ...options,
    })
  }

  /**
   * Read a file
   */
  public read<ThrowOnError extends boolean = false>(options: Options<FileReadData, ThrowOnError>) {
    return (options.client ?? this._client).get<FileReadResponses, unknown, ThrowOnError>({
      url: "/file/content",
      ...options,
    })
  }

  /**
   * Get file status
   */
  public status<ThrowOnError extends boolean = false>(options?: Options<FileStatusData, ThrowOnError>) {
    return (options?.client ?? this._client).get<FileStatusResponses, unknown, ThrowOnError>({
      url: "/file/status",
      ...options,
    })
  }
}

class Tui extends _HeyApiClient {
  /**
   * Append prompt to the TUI
   */
  public appendPrompt<ThrowOnError extends boolean = false>(options?: Options<TuiAppendPromptData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiAppendPromptResponses, unknown, ThrowOnError>({
      url: "/tui/append-prompt",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
    })
  }

  /**
   * Open the help dialog
   */
  public openHelp<ThrowOnError extends boolean = false>(options?: Options<TuiOpenHelpData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiOpenHelpResponses, unknown, ThrowOnError>({
      url: "/tui/open-help",
      ...options,
    })
  }

  /**
   * Open the session dialog
   */
  public openSessions<ThrowOnError extends boolean = false>(options?: Options<TuiOpenSessionsData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiOpenSessionsResponses, unknown, ThrowOnError>({
      url: "/tui/open-sessions",
      ...options,
    })
  }

  /**
   * Open the theme dialog
   */
  public openThemes<ThrowOnError extends boolean = false>(options?: Options<TuiOpenThemesData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiOpenThemesResponses, unknown, ThrowOnError>({
      url: "/tui/open-themes",
      ...options,
    })
  }

  /**
   * Open the model dialog
   */
  public openModels<ThrowOnError extends boolean = false>(options?: Options<TuiOpenModelsData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiOpenModelsResponses, unknown, ThrowOnError>({
      url: "/tui/open-models",
      ...options,
    })
  }

  /**
   * Submit the prompt
   */
  public submitPrompt<ThrowOnError extends boolean = false>(options?: Options<TuiSubmitPromptData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiSubmitPromptResponses, unknown, ThrowOnError>({
      url: "/tui/submit-prompt",
      ...options,
    })
  }

  /**
   * Clear the prompt
   */
  public clearPrompt<ThrowOnError extends boolean = false>(options?: Options<TuiClearPromptData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiClearPromptResponses, unknown, ThrowOnError>({
      url: "/tui/clear-prompt",
      ...options,
    })
  }

  /**
   * Execute a TUI command (e.g. agent_cycle)
   */
  public executeCommand<ThrowOnError extends boolean = false>(options?: Options<TuiExecuteCommandData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiExecuteCommandResponses, unknown, ThrowOnError>({
      url: "/tui/execute-command",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
    })
  }

  /**
   * Show a toast notification in the TUI
   */
  public showToast<ThrowOnError extends boolean = false>(options?: Options<TuiShowToastData, ThrowOnError>) {
    return (options?.client ?? this._client).post<TuiShowToastResponses, unknown, ThrowOnError>({
      url: "/tui/show-toast",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
    })
  }
}

class Auth extends _HeyApiClient {
  /**
   * Set authentication credentials
   */
  public set<ThrowOnError extends boolean = false>(options: Options<AuthSetData, ThrowOnError>) {
    return (options.client ?? this._client).put<AuthSetResponses, AuthSetErrors, ThrowOnError>({
      url: "/auth/{id}",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }
}

export class OpencodeClient extends _HeyApiClient {
  /**
   * Respond to a permission request
   */
  public postSessionByIdPermissionsByPermissionId<ThrowOnError extends boolean = false>(
    options: Options<PostSessionByIdPermissionsByPermissionIdData, ThrowOnError>,
  ) {
    return (options.client ?? this._client).post<
      PostSessionByIdPermissionsByPermissionIdResponses,
      unknown,
      ThrowOnError
    >({
      url: "/session/{id}/permissions/{permissionID}",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    })
  }
  event = new Event({ client: this._client })
  app = new App({ client: this._client })
  config = new Config({ client: this._client })
  session = new Session({ client: this._client })
  command = new Command({ client: this._client })
  find = new Find({ client: this._client })
  file = new File({ client: this._client })
  tui = new Tui({ client: this._client })
  auth = new Auth({ client: this._client })
}
